# 高精度点云精配准程序

本程序实现了基于ICP算法的高精度点云精配准，用于将blue和green点云精确配准到red点云。

## 功能特点

- 🎯 **超高精度**: 使用多尺度ICP算法，精度可达0.01mm级别
- 🚀 **高效算法**: 支持体素下采样和随机采样加速
- 📊 **详细统计**: 提供配准误差、适应度等详细统计信息
- 🔧 **两种实现**: Open3D版本（功能完整）和NumPy版本（轻量级）
- 📏 **单位统一**: 所有计算和输出均使用毫米(mm)单位

## 文件说明

- `point_cloud_registration.py`: 基于Open3D的完整版本（推荐）
- `numpy_icp_registration.py`: 纯NumPy/SciPy实现的轻量版本
- `requirements.txt`: Python依赖包列表
- `待配平面/`: 包含三个点云文件（red.xyz, blue.xyz, green.xyz）
- `b2r.txt`: Blue到Red的粗配准矩阵
- `g2r.txt`: Green到Red的粗配准矩阵

## 安装依赖

### 方法1: 完整版本（推荐）
```bash
pip install -r requirements.txt
```

### 方法2: 轻量版本（如果Open3D安装有问题）
```bash
pip install numpy scipy
```

## 使用方法

### 运行完整版本
```bash
python point_cloud_registration.py
```

### 运行轻量版本
```bash
python numpy_icp_registration.py
```

## 输出结果

程序运行后会生成以下文件：

- `b2r_fine.txt`: Blue到Red的精配准矩阵（Open3D版本）
- `g2r_fine.txt`: Green到Red的精配准矩阵（Open3D版本）
- `b2r_fine_numpy.txt`: Blue到Red的精配准矩阵（NumPy版本）
- `g2r_fine_numpy.txt`: Green到Red的精配准矩阵（NumPy版本）

## 算法参数

### Open3D版本参数
- 体素大小: 0.05mm（超高精度）
- 最大迭代次数: 2000
- 收敛容差: 1e-10
- 多尺度配准: 3个尺度层级

### NumPy版本参数
- 体素大小: 0.5mm
- 最大迭代次数: 500
- 收敛容差: 1e-8
- 采样比例: 5%

## 精度说明

- **粗配准**: 使用提供的RT矩阵作为初始估计
- **精配准**: 通过ICP算法迭代优化，可达到0.01-0.1mm的精度
- **单位统一**: 所有计算均使用毫米(mm)单位，确保精度一致性

## 注意事项

1. 确保点云文件格式正确（xyz格式，空格分隔）
2. 程序会自动过滤全零点
3. 大点云会自动下采样以提高计算效率
4. 建议使用SSD硬盘以提高文件读取速度

## 故障排除

如果遇到内存不足问题，可以：
1. 使用NumPy版本（内存占用更少）
2. 调整采样比例参数
3. 增加体素下采样大小
