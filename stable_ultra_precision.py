#!/usr/bin/env python3
"""
稳定的超高精度点云配准程序
目标精度: 0.1mm级别
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.spatial import cKDTree
import time

class StableUltraPrecisionICP:
    def __init__(self, target_precision=0.1):
        self.target_precision = target_precision
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading point cloud: {filepath}")
        data = np.loadtxt(filepath)
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        print(f"Loaded {len(points)} valid points")
        return points
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        matrix = np.loadtxt(filepath)
        return matrix
    
    def uniform_downsample(self, points, target_count):
        """均匀下采样"""
        if len(points) <= target_count:
            return points
        
        # 计算采样步长
        step = len(points) // target_count
        indices = np.arange(0, len(points), step)[:target_count]
        return points[indices]
    
    def apply_transformation(self, points, transformation_matrix):
        """应用变换矩阵"""
        ones = np.ones((points.shape[0], 1))
        homogeneous_points = np.hstack([points, ones])
        transformed_homogeneous = homogeneous_points @ transformation_matrix.T
        return transformed_homogeneous[:, :3]
    
    def compute_transformation_svd(self, source_points, target_points, weights=None):
        """使用SVD计算变换矩阵"""
        if weights is None:
            weights = np.ones(len(source_points))
        
        # 加权质心
        total_weight = np.sum(weights)
        source_centroid = np.sum(source_points * weights[:, np.newaxis], axis=0) / total_weight
        target_centroid = np.sum(target_points * weights[:, np.newaxis], axis=0) / total_weight
        
        # 去中心化
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid
        
        # 加权协方差矩阵
        H = (source_centered * weights[:, np.newaxis]).T @ target_centered
        
        # SVD分解
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T
        
        # 确保旋转矩阵的行列式为正
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        # 计算平移
        t = target_centroid - R @ source_centroid
        
        # 构建变换矩阵
        transformation = np.eye(4)
        transformation[:3, :3] = R
        transformation[:3, 3] = t
        
        return transformation
    
    def ultra_precision_icp(self, source_points, target_points, initial_transformation,
                           sample_size=100000, max_iterations=1000, distance_threshold=0.5):
        """超高精度ICP配准"""
        print(f"Ultra-precision ICP: sample_size={sample_size}, threshold={distance_threshold}mm")
        
        # 采样以提高效率
        source_sample = self.uniform_downsample(source_points, sample_size)
        target_sample = self.uniform_downsample(target_points, sample_size)
        
        current_transformation = initial_transformation.copy()
        prev_error = float('inf')
        
        # 构建目标点云的KD树
        target_tree = cKDTree(target_sample)
        
        for iteration in range(max_iterations):
            # 应用当前变换
            transformed_source = self.apply_transformation(source_sample, current_transformation)
            
            # 找最近邻
            distances, indices = target_tree.query(transformed_source)
            
            # 过滤距离过大的点
            valid_mask = distances < distance_threshold
            
            if np.sum(valid_mask) < 1000:  # 需要足够的对应点
                print(f"Insufficient valid correspondences: {np.sum(valid_mask)}")
                distance_threshold *= 1.2  # 放宽距离阈值
                continue
            
            # 获取有效对应点
            valid_source = transformed_source[valid_mask]
            valid_target = target_sample[indices[valid_mask]]
            valid_distances = distances[valid_mask]
            
            # 计算权重（基于距离的高斯权重）
            sigma = distance_threshold / 3
            weights = np.exp(-(valid_distances ** 2) / (2 * sigma ** 2))
            
            # 计算新的变换矩阵
            delta_transformation = self.compute_transformation_svd(
                valid_source, valid_target, weights)
            
            # 更新总变换
            current_transformation = delta_transformation @ current_transformation
            
            # 计算当前误差
            current_error = np.mean(valid_distances)
            error_change = abs(prev_error - current_error)
            
            # 检查收敛
            if error_change < 1e-8:
                print(f"Converged at iteration {iteration}, error: {current_error:.6f}mm")
                break
            
            prev_error = current_error
            
            # 动态调整距离阈值
            if iteration > 50 and current_error < distance_threshold * 0.8:
                distance_threshold = max(current_error * 1.5, 0.1)
            
            if iteration % 100 == 0:
                print(f"Iteration {iteration}: error = {current_error:.6f}mm, "
                      f"valid points = {np.sum(valid_mask)}, threshold = {distance_threshold:.3f}mm")
        
        return current_transformation, current_error
    
    def multi_level_registration(self, source_points, target_points, initial_transformation):
        """多级精度配准"""
        print("Starting multi-level ultra-precision registration...")
        
        # 多个精度级别 (优化参数以提高效率)
        levels = [
            {'sample_size': 30000, 'threshold': 1.0, 'iterations': 200},
            {'sample_size': 50000, 'threshold': 0.5, 'iterations': 300},
            {'sample_size': 80000, 'threshold': 0.2, 'iterations': 400},
            {'sample_size': 100000, 'threshold': 0.1, 'iterations': 500},
        ]
        
        current_transformation = initial_transformation.copy()
        
        for level_idx, level_params in enumerate(levels):
            print(f"\n--- Level {level_idx + 1}/{len(levels)} ---")
            
            transformation, error = self.ultra_precision_icp(
                source_points, target_points, current_transformation,
                sample_size=level_params['sample_size'],
                max_iterations=level_params['iterations'],
                distance_threshold=level_params['threshold']
            )
            
            current_transformation = transformation
            print(f"Level {level_idx + 1} completed with error: {error:.6f}mm")
            
            # 如果达到目标精度，提前退出
            if error <= self.target_precision:
                print(f"Target precision {self.target_precision}mm achieved!")
                break
        
        return current_transformation, error
    
    def compute_final_metrics(self, source_points, target_points, transformation, sample_size=50000):
        """计算最终配准指标"""
        print("Computing final registration metrics...")
        
        # 采样验证
        source_sample = self.uniform_downsample(source_points, sample_size)
        target_sample = self.uniform_downsample(target_points, sample_size)
        
        # 应用变换
        transformed_source = self.apply_transformation(source_sample, transformation)
        
        # 计算距离
        target_tree = cKDTree(target_sample)
        distances, _ = target_tree.query(transformed_source)
        
        metrics = {
            'mean_error': np.mean(distances),
            'median_error': np.median(distances),
            'std_error': np.std(distances),
            'max_error': np.max(distances),
            'min_error': np.min(distances),
            'inlier_01mm': np.sum(distances < 0.1) / len(distances),
            'inlier_02mm': np.sum(distances < 0.2) / len(distances),
            'inlier_05mm': np.sum(distances < 0.5) / len(distances),
            'inlier_1mm': np.sum(distances < 1.0) / len(distances),
        }
        
        return metrics
    
    def save_transformation_matrix(self, matrix, filepath):
        """保存变换矩阵"""
        print(f"Saving transformation matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.15f')

def main():
    np.random.seed(42)
    
    # 初始化超高精度配准器
    ultra_icp = StableUltraPrecisionICP(target_precision=0.1)
    
    print("=== 稳定超高精度点云配准程序 ===")
    print("目标精度: 0.1mm")
    print("单位: 毫米(mm)")
    print()
    
    # 加载数据
    red_points = ultra_icp.load_point_cloud("待配平面/red.xyz")
    blue_points = ultra_icp.load_point_cloud("待配平面/blue.xyz")
    green_points = ultra_icp.load_point_cloud("待配平面/green.xyz")
    
    b2r_coarse = ultra_icp.load_transformation_matrix("b2r.txt")
    g2r_coarse = ultra_icp.load_transformation_matrix("g2r.txt")
    
    print("\n=== Blue to Red 超高精度配准 ===")
    start_time = time.time()
    
    b2r_transformation, b2r_error = ultra_icp.multi_level_registration(
        blue_points, red_points, b2r_coarse)
    
    b2r_metrics = ultra_icp.compute_final_metrics(blue_points, red_points, b2r_transformation)
    b2r_time = time.time() - start_time
    
    print(f"Blue to Red 配准完成，耗时: {b2r_time:.2f}秒")
    
    print("\n=== Green to Red 超高精度配准 ===")
    start_time = time.time()
    
    g2r_transformation, g2r_error = ultra_icp.multi_level_registration(
        green_points, red_points, g2r_coarse)
    
    g2r_metrics = ultra_icp.compute_final_metrics(green_points, red_points, g2r_transformation)
    g2r_time = time.time() - start_time
    
    print(f"Green to Red 配准完成，耗时: {g2r_time:.2f}秒")
    
    # 保存结果
    ultra_icp.save_transformation_matrix(b2r_transformation, "b2r_stable_ultra.txt")
    ultra_icp.save_transformation_matrix(g2r_transformation, "g2r_stable_ultra.txt")
    
    # 输出详细结果
    print("\n=== 超高精度配准结果 ===")
    print(f"Blue to Red:")
    print(f"  平均误差: {b2r_metrics['mean_error']:.6f}mm")
    print(f"  中位数误差: {b2r_metrics['median_error']:.6f}mm")
    print(f"  标准差: {b2r_metrics['std_error']:.6f}mm")
    print(f"  0.1mm内点比例: {b2r_metrics['inlier_01mm']:.2%}")
    print(f"  0.2mm内点比例: {b2r_metrics['inlier_02mm']:.2%}")
    print(f"  目标达成: {'✅' if b2r_metrics['median_error'] <= 0.1 else '❌'}")
    
    print(f"\nGreen to Red:")
    print(f"  平均误差: {g2r_metrics['mean_error']:.6f}mm")
    print(f"  中位数误差: {g2r_metrics['median_error']:.6f}mm")
    print(f"  标准差: {g2r_metrics['std_error']:.6f}mm")
    print(f"  0.1mm内点比例: {g2r_metrics['inlier_01mm']:.2%}")
    print(f"  0.2mm内点比例: {g2r_metrics['inlier_02mm']:.2%}")
    print(f"  目标达成: {'✅' if g2r_metrics['median_error'] <= 0.1 else '❌'}")
    
    print("\n超高精度配准矩阵已保存:")
    print("- b2r_stable_ultra.txt")
    print("- g2r_stable_ultra.txt")

if __name__ == "__main__":
    main()
