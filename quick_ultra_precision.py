#!/usr/bin/env python3
"""
快速超高精度点云配准程序
专门用于生成0.1mm级别精度的配准矩阵
"""

import numpy as np
from scipy.spatial import cKDTree
import time

class QuickUltraPrecisionICP:
    def __init__(self):
        self.target_precision = 0.1
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading: {filepath}")
        data = np.loadtxt(filepath)
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        print(f"Loaded {len(points)} points")
        return points
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        return np.loadtxt(filepath)
    
    def downsample_uniform(self, points, target_count):
        """均匀下采样"""
        if len(points) <= target_count:
            return points
        step = len(points) // target_count
        return points[::step][:target_count]
    
    def apply_transformation(self, points, matrix):
        """应用变换矩阵"""
        ones = np.ones((points.shape[0], 1))
        homogeneous = np.hstack([points, ones])
        transformed = homogeneous @ matrix.T
        return transformed[:, :3]
    
    def compute_transformation_svd(self, source, target, weights=None):
        """SVD计算变换矩阵"""
        if weights is None:
            weights = np.ones(len(source))
        
        # 加权质心
        total_weight = np.sum(weights)
        source_centroid = np.sum(source * weights[:, np.newaxis], axis=0) / total_weight
        target_centroid = np.sum(target * weights[:, np.newaxis], axis=0) / total_weight
        
        # 去中心化
        source_centered = source - source_centroid
        target_centered = target - target_centroid
        
        # 协方差矩阵
        H = (source_centered * weights[:, np.newaxis]).T @ target_centered
        
        # SVD分解
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T
        
        # 确保旋转矩阵
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        # 平移
        t = target_centroid - R @ source_centroid
        
        # 构建4x4矩阵
        transformation = np.eye(4)
        transformation[:3, :3] = R
        transformation[:3, 3] = t
        
        return transformation
    
    def ultra_precision_icp(self, source, target, initial_matrix, max_iterations=300):
        """超高精度ICP"""
        print("Starting ultra-precision ICP...")
        
        # 采样以提高效率
        source_sample = self.downsample_uniform(source, 50000)
        target_sample = self.downsample_uniform(target, 50000)
        
        current_matrix = initial_matrix.copy()
        target_tree = cKDTree(target_sample)
        
        best_error = float('inf')
        best_matrix = current_matrix.copy()
        
        for iteration in range(max_iterations):
            # 应用变换
            transformed_source = self.apply_transformation(source_sample, current_matrix)
            
            # 最近邻搜索
            distances, indices = target_tree.query(transformed_source)
            
            # 过滤异常值
            threshold = np.percentile(distances, 90)  # 使用90%分位数作为阈值
            valid_mask = distances < threshold
            
            if np.sum(valid_mask) < 1000:
                print(f"Insufficient correspondences at iteration {iteration}")
                break
            
            # 有效对应点
            valid_source = transformed_source[valid_mask]
            valid_target = target_sample[indices[valid_mask]]
            valid_distances = distances[valid_mask]
            
            # 计算权重
            sigma = np.std(valid_distances)
            weights = np.exp(-(valid_distances ** 2) / (2 * sigma ** 2))
            
            # 计算变换
            delta_matrix = self.compute_transformation_svd(valid_source, valid_target, weights)
            current_matrix = delta_matrix @ current_matrix
            
            # 计算误差
            current_error = np.mean(valid_distances)
            
            # 保存最佳结果
            if current_error < best_error:
                best_error = current_error
                best_matrix = current_matrix.copy()
            
            # 检查收敛
            if iteration > 10 and current_error < 0.15:  # 接近目标精度时更严格
                if iteration % 10 == 0:
                    print(f"Iteration {iteration}: error = {current_error:.6f}mm")
                
                if current_error < 1e-6:
                    print(f"Converged at iteration {iteration}")
                    break
            elif iteration % 50 == 0:
                print(f"Iteration {iteration}: error = {current_error:.6f}mm")
        
        print(f"Final error: {best_error:.6f}mm")
        return best_matrix, best_error
    
    def refine_precision(self, source, target, initial_matrix):
        """精度精化"""
        print("Refining precision...")
        
        # 使用更大的采样进行精化
        source_refined = self.downsample_uniform(source, 100000)
        target_refined = self.downsample_uniform(target, 100000)
        
        current_matrix = initial_matrix.copy()
        target_tree = cKDTree(target_refined)
        
        for iteration in range(100):
            transformed_source = self.apply_transformation(source_refined, current_matrix)
            distances, indices = target_tree.query(transformed_source)
            
            # 使用更严格的阈值
            threshold = min(0.2, np.percentile(distances, 85))
            valid_mask = distances < threshold
            
            if np.sum(valid_mask) < 5000:
                break
            
            valid_source = transformed_source[valid_mask]
            valid_target = target_refined[indices[valid_mask]]
            valid_distances = distances[valid_mask]
            
            # 高精度权重
            weights = np.exp(-valid_distances / 0.05)
            
            delta_matrix = self.compute_transformation_svd(valid_source, valid_target, weights)
            current_matrix = delta_matrix @ current_matrix
            
            current_error = np.mean(valid_distances)
            
            if iteration % 20 == 0:
                print(f"Refinement iteration {iteration}: error = {current_error:.6f}mm")
            
            if current_error < 1e-7:
                break
        
        final_error = np.mean(distances[valid_mask])
        print(f"Refined error: {final_error:.6f}mm")
        return current_matrix, final_error
    
    def compute_final_metrics(self, source, target, matrix):
        """计算最终指标"""
        sample_source = self.downsample_uniform(source, 30000)
        sample_target = self.downsample_uniform(target, 30000)
        
        transformed = self.apply_transformation(sample_source, matrix)
        tree = cKDTree(sample_target)
        distances, _ = tree.query(transformed)
        
        return {
            'mean': np.mean(distances),
            'median': np.median(distances),
            'std': np.std(distances),
            'max': np.max(distances),
            'inlier_01mm': np.sum(distances < 0.1) / len(distances),
            'inlier_02mm': np.sum(distances < 0.2) / len(distances),
        }
    
    def save_matrix(self, matrix, filepath):
        """保存矩阵"""
        print(f"Saving matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.15f')

def main():
    print("=== 快速超高精度点云配准 ===")
    print("目标精度: 0.1mm级别")
    print()
    
    icp = QuickUltraPrecisionICP()
    
    # 加载数据
    red_points = icp.load_point_cloud("待配平面/red.xyz")
    blue_points = icp.load_point_cloud("待配平面/blue.xyz")
    green_points = icp.load_point_cloud("待配平面/green.xyz")
    
    b2r_coarse = icp.load_transformation_matrix("b2r.txt")
    g2r_coarse = icp.load_transformation_matrix("g2r.txt")
    
    print("\n=== Blue to Red 超高精度配准 ===")
    start_time = time.time()
    
    # 第一阶段：超高精度ICP
    b2r_matrix, b2r_error = icp.ultra_precision_icp(blue_points, red_points, b2r_coarse)
    
    # 第二阶段：精度精化
    b2r_final_matrix, b2r_final_error = icp.refine_precision(blue_points, red_points, b2r_matrix)
    
    # 计算最终指标
    b2r_metrics = icp.compute_final_metrics(blue_points, red_points, b2r_final_matrix)
    
    b2r_time = time.time() - start_time
    print(f"Blue to Red 配准完成，耗时: {b2r_time:.2f}秒")
    
    print("\n=== Green to Red 超高精度配准 ===")
    start_time = time.time()
    
    # 第一阶段：超高精度ICP
    g2r_matrix, g2r_error = icp.ultra_precision_icp(green_points, red_points, g2r_coarse)
    
    # 第二阶段：精度精化
    g2r_final_matrix, g2r_final_error = icp.refine_precision(green_points, red_points, g2r_matrix)
    
    # 计算最终指标
    g2r_metrics = icp.compute_final_metrics(green_points, red_points, g2r_final_matrix)
    
    g2r_time = time.time() - start_time
    print(f"Green to Red 配准完成，耗时: {g2r_time:.2f}秒")
    
    # 保存结果
    icp.save_matrix(b2r_final_matrix, "b2r_ultra_precision_final.txt")
    icp.save_matrix(g2r_final_matrix, "g2r_ultra_precision_final.txt")
    
    # 输出结果
    print("\n=== 超高精度配准结果 ===")
    print(f"Blue to Red:")
    print(f"  最终误差: {b2r_final_error:.6f}mm")
    print(f"  平均误差: {b2r_metrics['mean']:.6f}mm")
    print(f"  中位数误差: {b2r_metrics['median']:.6f}mm")
    print(f"  0.1mm内点比例: {b2r_metrics['inlier_01mm']:.2%}")
    print(f"  目标达成: {'✅' if b2r_metrics['median'] <= 0.1 else '❌'}")
    
    print(f"\nGreen to Red:")
    print(f"  最终误差: {g2r_final_error:.6f}mm")
    print(f"  平均误差: {g2r_metrics['mean']:.6f}mm")
    print(f"  中位数误差: {g2r_metrics['median']:.6f}mm")
    print(f"  0.1mm内点比例: {g2r_metrics['inlier_01mm']:.2%}")
    print(f"  目标达成: {'✅' if g2r_metrics['median'] <= 0.1 else '❌'}")
    
    print("\n超高精度配准矩阵已保存:")
    print("- b2r_ultra_precision_final.txt")
    print("- g2r_ultra_precision_final.txt")
    
    print(f"\nBlue to Red 最终矩阵:")
    print(b2r_final_matrix)
    
    print(f"\nGreen to Red 最终矩阵:")
    print(g2r_final_matrix)

if __name__ == "__main__":
    main()
