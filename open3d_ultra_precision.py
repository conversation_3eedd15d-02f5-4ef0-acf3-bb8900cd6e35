#!/usr/bin/env python3
"""
基于Open3D的超高精度点云配准程序
目标精度: 0.1mm级别
"""

import numpy as np
import open3d as o3d
import time

class Open3DUltraPrecision:
    def __init__(self, target_precision=0.1):
        """
        初始化超高精度配准器
        
        Args:
            target_precision: 目标精度 (mm)
        """
        self.target_precision = target_precision
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading point cloud: {filepath}")
        
        # 读取数据
        data = np.loadtxt(filepath)
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        
        # 创建Open3D点云
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        print(f"Loaded {len(points)} valid points")
        return pcd
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        print(f"Loading transformation matrix: {filepath}")
        matrix = np.loadtxt(filepath)
        return matrix
    
    def preprocess_ultra_precision(self, pcd, voxel_size):
        """超高精度预处理"""
        print(f"Ultra-precision preprocessing with voxel size: {voxel_size}mm")
        
        # 下采样
        pcd_down = pcd.voxel_down_sample(voxel_size)
        
        # 估计法向量（高精度参数）
        pcd_down.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(
                radius=voxel_size * 3, max_nn=50))
        
        # 法向量方向一致性
        pcd_down.orient_normals_consistent_tangent_plane(100)
        
        print(f"Preprocessed to {len(pcd_down.points)} points")
        return pcd_down
    
    def ultra_precision_icp(self, source, target, initial_transformation, voxel_size):
        """超高精度ICP配准"""
        print(f"Ultra-precision ICP with voxel size: {voxel_size}mm")
        
        # 预处理
        source_down = self.preprocess_ultra_precision(source, voxel_size)
        target_down = self.preprocess_ultra_precision(target, voxel_size)
        
        # 设置超高精度ICP参数
        threshold = voxel_size * 1.5  # 更严格的距离阈值
        
        # Point-to-Plane ICP（更高精度）
        reg_p2p = o3d.pipelines.registration.registration_icp(
            source_down, target_down, threshold, initial_transformation,
            o3d.pipelines.registration.TransformationEstimationPointToPlane(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                max_iteration=3000,      # 增加迭代次数
                relative_fitness=1e-12,  # 更严格的收敛条件
                relative_rmse=1e-12
            )
        )
        
        print(f"ICP Results:")
        print(f"  Fitness: {reg_p2p.fitness:.8f}")
        print(f"  RMSE: {reg_p2p.inlier_rmse:.8f}mm")
        
        return reg_p2p
    
    def multi_scale_ultra_icp(self, source, target, initial_transformation):
        """多尺度超高精度ICP"""
        print("Starting multi-scale ultra-precision ICP...")
        
        # 超高精度多尺度配准
        voxel_sizes = [0.2, 0.1, 0.05, 0.02, 0.01]  # 从0.2mm到0.01mm
        current_transformation = initial_transformation.copy()
        
        for i, voxel_size in enumerate(voxel_sizes):
            print(f"\nScale {i+1}/{len(voxel_sizes)}: voxel_size = {voxel_size}mm")
            
            reg_result = self.ultra_precision_icp(
                source, target, current_transformation, voxel_size)
            
            current_transformation = reg_result.transformation
            
            # 如果RMSE已经达到目标精度，可以提前结束
            if reg_result.inlier_rmse <= self.target_precision:
                print(f"Target precision {self.target_precision}mm achieved at scale {i+1}!")
                break
        
        return current_transformation, reg_result
    
    def colored_icp_refinement(self, source, target, transformation):
        """彩色ICP精化（如果有颜色信息）"""
        print("Attempting colored ICP refinement...")
        
        # 为点云添加虚拟颜色（基于位置）
        source_colored = source.paint_uniform_color([1, 0, 0])  # 红色
        target_colored = target.paint_uniform_color([0, 1, 0])  # 绿色
        
        # 预处理
        voxel_size = 0.01
        source_down = self.preprocess_ultra_precision(source_colored, voxel_size)
        target_down = self.preprocess_ultra_precision(target_colored, voxel_size)
        
        # 彩色ICP
        reg_colored = o3d.pipelines.registration.registration_colored_icp(
            source_down, target_down, voxel_size, transformation,
            o3d.pipelines.registration.TransformationEstimationForColoredICP(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                relative_fitness=1e-12,
                relative_rmse=1e-12,
                max_iteration=2000
            )
        )
        
        print(f"Colored ICP Results:")
        print(f"  Fitness: {reg_colored.fitness:.8f}")
        print(f"  RMSE: {reg_colored.inlier_rmse:.8f}mm")
        
        return reg_colored
    
    def generalized_icp_refinement(self, source, target, transformation):
        """广义ICP精化"""
        print("Generalized ICP refinement...")
        
        # 预处理
        voxel_size = 0.01
        source_down = self.preprocess_ultra_precision(source, voxel_size)
        target_down = self.preprocess_ultra_precision(target, voxel_size)
        
        # 广义ICP
        reg_gicp = o3d.pipelines.registration.registration_generalized_icp(
            source_down, target_down, voxel_size * 1.5, transformation,
            o3d.pipelines.registration.TransformationEstimationForGeneralizedICP(),
            o3d.pipelines.registration.ICPConvergenceCriteria(
                relative_fitness=1e-12,
                relative_rmse=1e-12,
                max_iteration=2000
            )
        )
        
        print(f"Generalized ICP Results:")
        print(f"  Fitness: {reg_gicp.fitness:.8f}")
        print(f"  RMSE: {reg_gicp.inlier_rmse:.8f}mm")
        
        return reg_gicp
    
    def compute_final_error(self, source, target, transformation):
        """计算最终配准误差"""
        source_transformed = source.transform(transformation)
        distances = np.asarray(source_transformed.compute_point_cloud_distance(target))
        
        mean_error = np.mean(distances)
        median_error = np.median(distances)
        std_error = np.std(distances)
        max_error = np.max(distances)
        
        # 计算不同阈值下的内点比例
        inlier_01mm = np.sum(distances < 0.1) / len(distances)
        inlier_02mm = np.sum(distances < 0.2) / len(distances)
        inlier_05mm = np.sum(distances < 0.5) / len(distances)
        
        return {
            'mean': mean_error,
            'median': median_error,
            'std': std_error,
            'max': max_error,
            'inlier_01mm': inlier_01mm,
            'inlier_02mm': inlier_02mm,
            'inlier_05mm': inlier_05mm
        }
    
    def save_transformation_matrix(self, matrix, filepath):
        """保存变换矩阵"""
        print(f"Saving ultra-precision transformation matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.15f')
        print("Matrix saved successfully")

def main():
    # 初始化超高精度配准器
    ultra_reg = Open3DUltraPrecision(target_precision=0.1)
    
    print("=== Open3D超高精度点云配准程序 ===")
    print("目标精度: 0.1mm")
    print("单位: 毫米(mm)")
    print()
    
    try:
        # 加载数据
        red_pcd = ultra_reg.load_point_cloud("待配平面/red.xyz")
        blue_pcd = ultra_reg.load_point_cloud("待配平面/blue.xyz")
        green_pcd = ultra_reg.load_point_cloud("待配平面/green.xyz")
        
        b2r_coarse = ultra_reg.load_transformation_matrix("b2r.txt")
        g2r_coarse = ultra_reg.load_transformation_matrix("g2r.txt")
        
        print("\n=== Blue to Red 超高精度配准 ===")
        start_time = time.time()
        
        # 多尺度超高精度配准
        b2r_transformation, b2r_result = ultra_reg.multi_scale_ultra_icp(
            blue_pcd, red_pcd, b2r_coarse)
        
        # 尝试广义ICP精化
        try:
            b2r_gicp = ultra_reg.generalized_icp_refinement(
                blue_pcd, red_pcd, b2r_transformation)
            if b2r_gicp.inlier_rmse < b2r_result.inlier_rmse:
                b2r_transformation = b2r_gicp.transformation
                b2r_result = b2r_gicp
                print("Generalized ICP improved the result!")
        except Exception as e:
            print(f"Generalized ICP failed: {e}")
        
        # 计算最终误差
        b2r_errors = ultra_reg.compute_final_error(blue_pcd, red_pcd, b2r_transformation)
        
        b2r_time = time.time() - start_time
        print(f"Blue to Red 配准完成，耗时: {b2r_time:.2f}秒")
        
        print("\n=== Green to Red 超高精度配准 ===")
        start_time = time.time()
        
        # 多尺度超高精度配准
        g2r_transformation, g2r_result = ultra_reg.multi_scale_ultra_icp(
            green_pcd, red_pcd, g2r_coarse)
        
        # 尝试广义ICP精化
        try:
            g2r_gicp = ultra_reg.generalized_icp_refinement(
                green_pcd, red_pcd, g2r_transformation)
            if g2r_gicp.inlier_rmse < g2r_result.inlier_rmse:
                g2r_transformation = g2r_gicp.transformation
                g2r_result = g2r_gicp
                print("Generalized ICP improved the result!")
        except Exception as e:
            print(f"Generalized ICP failed: {e}")
        
        # 计算最终误差
        g2r_errors = ultra_reg.compute_final_error(green_pcd, red_pcd, g2r_transformation)
        
        g2r_time = time.time() - start_time
        print(f"Green to Red 配准完成，耗时: {g2r_time:.2f}秒")
        
        # 保存结果
        ultra_reg.save_transformation_matrix(b2r_transformation, "b2r_open3d_ultra.txt")
        ultra_reg.save_transformation_matrix(g2r_transformation, "g2r_open3d_ultra.txt")
        
        # 输出详细结果
        print("\n=== 超高精度配准结果 ===")
        print(f"Blue to Red:")
        print(f"  RMSE: {b2r_result.inlier_rmse:.6f}mm")
        print(f"  平均误差: {b2r_errors['mean']:.6f}mm")
        print(f"  中位数误差: {b2r_errors['median']:.6f}mm")
        print(f"  0.1mm内点比例: {b2r_errors['inlier_01mm']:.2%}")
        print(f"  目标达成: {'✅' if b2r_errors['median'] <= 0.1 else '❌'}")
        
        print(f"\nGreen to Red:")
        print(f"  RMSE: {g2r_result.inlier_rmse:.6f}mm")
        print(f"  平均误差: {g2r_errors['mean']:.6f}mm")
        print(f"  中位数误差: {g2r_errors['median']:.6f}mm")
        print(f"  0.1mm内点比例: {g2r_errors['inlier_01mm']:.2%}")
        print(f"  目标达成: {'✅' if g2r_errors['median'] <= 0.1 else '❌'}")
        
        print("\n超高精度配准矩阵已保存:")
        print("- b2r_open3d_ultra.txt")
        print("- g2r_open3d_ultra.txt")
        
    except ImportError:
        print("❌ Open3D未安装，请先安装: pip install open3d")
        print("或使用NumPy版本: python ultra_precision_registration.py")

if __name__ == "__main__":
    main()
