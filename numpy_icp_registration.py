#!/usr/bin/env python3
"""
纯NumPy/SciPy实现的高精度点云精配准程序
使用ICP算法进行精配准，基于粗配准矩阵进行初始化
单位统一为毫米(mm)
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.spatial import cKDTree
import time

class NumpyICP:
    def __init__(self, max_iterations=1000, tolerance=1e-8, sample_ratio=0.1):
        """
        初始化ICP配准器
        
        Args:
            max_iterations: 最大迭代次数
            tolerance: 收敛容差
            sample_ratio: 采样比例（用于加速计算）
        """
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        self.sample_ratio = sample_ratio
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading point cloud: {filepath}")
        
        # 读取xyz文件
        data = np.loadtxt(filepath)
        
        # 过滤掉全零点
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        
        print(f"Loaded {len(points)} valid points")
        return points
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        print(f"Loading transformation matrix: {filepath}")
        matrix = np.loadtxt(filepath)
        print(f"Transformation matrix:\n{matrix}")
        return matrix
    
    def downsample_points(self, points, voxel_size):
        """体素下采样"""
        if len(points) == 0:
            return points
            
        # 计算体素索引
        voxel_indices = np.floor(points / voxel_size).astype(int)
        
        # 使用字典去重
        unique_voxels = {}
        for i, voxel_idx in enumerate(voxel_indices):
            key = tuple(voxel_idx)
            if key not in unique_voxels:
                unique_voxels[key] = points[i]
        
        downsampled = np.array(list(unique_voxels.values()))
        print(f"Downsampled from {len(points)} to {len(downsampled)} points")
        return downsampled
    
    def random_sample(self, points, ratio):
        """随机采样"""
        if ratio >= 1.0:
            return points
        
        n_samples = int(len(points) * ratio)
        indices = np.random.choice(len(points), n_samples, replace=False)
        return points[indices]
    
    def apply_transformation(self, points, transformation_matrix):
        """应用变换矩阵到点云"""
        # 转换为齐次坐标
        ones = np.ones((points.shape[0], 1))
        homogeneous_points = np.hstack([points, ones])
        
        # 应用变换
        transformed_homogeneous = homogeneous_points @ transformation_matrix.T
        
        # 转换回3D坐标
        return transformed_homogeneous[:, :3]
    
    def find_nearest_neighbors(self, source_points, target_points):
        """找到最近邻点"""
        tree = cKDTree(target_points)
        distances, indices = tree.query(source_points)
        return distances, indices
    
    def compute_transformation(self, source_points, target_points):
        """计算最优变换矩阵（使用SVD）"""
        # 计算质心
        source_centroid = np.mean(source_points, axis=0)
        target_centroid = np.mean(target_points, axis=0)
        
        # 去中心化
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid
        
        # 计算协方差矩阵
        H = source_centered.T @ target_centered
        
        # SVD分解
        U, S, Vt = np.linalg.svd(H)
        
        # 计算旋转矩阵
        R = Vt.T @ U.T
        
        # 确保旋转矩阵的行列式为正
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        # 计算平移向量
        t = target_centroid - R @ source_centroid
        
        # 构建4x4变换矩阵
        transformation = np.eye(4)
        transformation[:3, :3] = R
        transformation[:3, 3] = t
        
        return transformation
    
    def icp_iteration(self, source_points, target_points, current_transformation):
        """单次ICP迭代"""
        # 应用当前变换
        transformed_source = self.apply_transformation(source_points, current_transformation)
        
        # 找到最近邻
        distances, indices = self.find_nearest_neighbors(transformed_source, target_points)
        
        # 计算对应点对
        corresponding_target = target_points[indices]
        
        # 过滤距离过大的点对
        distance_threshold = np.percentile(distances, 90)  # 使用90%分位数作为阈值
        valid_mask = distances < distance_threshold
        
        if np.sum(valid_mask) < 10:  # 至少需要10个有效点对
            return current_transformation, np.mean(distances), False
        
        valid_source = transformed_source[valid_mask]
        valid_target = corresponding_target[valid_mask]
        
        # 计算新的变换矩阵
        delta_transformation = self.compute_transformation(valid_source, valid_target)
        
        # 更新总变换矩阵
        new_transformation = delta_transformation @ current_transformation
        
        # 计算平均距离
        mean_distance = np.mean(distances[valid_mask])
        
        return new_transformation, mean_distance, True
    
    def register(self, source_points, target_points, initial_transformation):
        """执行ICP配准"""
        print("Starting ICP registration...")
        
        # 下采样以加速计算
        voxel_size = 0.5  # 0.5mm体素大小
        source_down = self.downsample_points(source_points, voxel_size)
        target_down = self.downsample_points(target_points, voxel_size)
        
        # 进一步随机采样
        source_sample = self.random_sample(source_down, self.sample_ratio)
        target_sample = self.random_sample(target_down, min(1.0, self.sample_ratio * 2))
        
        print(f"Using {len(source_sample)} source points and {len(target_sample)} target points")
        
        current_transformation = initial_transformation.copy()
        prev_error = float('inf')
        
        for iteration in range(self.max_iterations):
            # 执行一次ICP迭代
            new_transformation, mean_error, success = self.icp_iteration(
                source_sample, target_sample, current_transformation)
            
            if not success:
                print(f"ICP failed at iteration {iteration}")
                break
            
            # 检查收敛
            error_change = abs(prev_error - mean_error)
            if error_change < self.tolerance:
                print(f"Converged at iteration {iteration}, error change: {error_change:.8f}")
                break
            
            current_transformation = new_transformation
            prev_error = mean_error
            
            if iteration % 50 == 0:
                print(f"Iteration {iteration}: mean error = {mean_error:.6f}mm")
        
        # 使用更密集的采样进行最终精化
        print("Final refinement with denser sampling...")
        dense_source = self.random_sample(source_points, min(0.2, 10000/len(source_points)))
        dense_target = self.random_sample(target_points, min(0.2, 10000/len(target_points)))

        for _ in range(5):  # 最多5次精化迭代
            new_transformation, mean_error, success = self.icp_iteration(
                dense_source, dense_target, current_transformation)
            if not success:
                break
            current_transformation = new_transformation
        
        print(f"Final mean error: {mean_error:.6f}mm")
        return current_transformation, mean_error
    
    def save_transformation_matrix(self, matrix, filepath):
        """保存变换矩阵"""
        print(f"Saving transformation matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.12f')
        print("Matrix saved successfully")

def main():
    # 设置随机种子以确保可重复性
    np.random.seed(42)
    
    # 初始化ICP配准器
    icp = NumpyICP(
        max_iterations=500,
        tolerance=1e-8,
        sample_ratio=0.05  # 使用5%的点进行配准以加速
    )
    
    print("=== 高精度点云精配准程序 (NumPy版本) ===")
    print("单位: 毫米(mm)")
    print()
    
    # 加载点云数据
    red_points = icp.load_point_cloud("待配平面/red.xyz")
    blue_points = icp.load_point_cloud("待配平面/blue.xyz")
    green_points = icp.load_point_cloud("待配平面/green.xyz")
    
    # 加载粗配准矩阵
    b2r_coarse = icp.load_transformation_matrix("b2r.txt")
    g2r_coarse = icp.load_transformation_matrix("g2r.txt")
    
    print("\n=== Blue to Red 精配准 ===")
    start_time = time.time()
    
    # Blue到Red的精配准
    b2r_fine_matrix, b2r_error = icp.register(blue_points, red_points, b2r_coarse)
    
    b2r_time = time.time() - start_time
    print(f"Blue to Red 配准完成，耗时: {b2r_time:.2f}秒")
    
    print("\n=== Green to Red 精配准 ===")
    start_time = time.time()
    
    # Green到Red的精配准
    g2r_fine_matrix, g2r_error = icp.register(green_points, red_points, g2r_coarse)
    
    g2r_time = time.time() - start_time
    print(f"Green to Red 配准完成，耗时: {g2r_time:.2f}秒")
    
    # 保存精配准结果
    icp.save_transformation_matrix(b2r_fine_matrix, "b2r_fine_numpy.txt")
    icp.save_transformation_matrix(g2r_fine_matrix, "g2r_fine_numpy.txt")
    
    # 输出最终结果
    print("\n=== 精配准结果总结 ===")
    print(f"Blue to Red 精配准矩阵:")
    print(b2r_fine_matrix)
    print(f"最终平均误差: {b2r_error:.6f}mm")
    
    print(f"\nGreen to Red 精配准矩阵:")
    print(g2r_fine_matrix)
    print(f"最终平均误差: {g2r_error:.6f}mm")
    
    print("\n精配准矩阵已保存到:")
    print("- b2r_fine_numpy.txt (Blue to Red)")
    print("- g2r_fine_numpy.txt (Green to Red)")

if __name__ == "__main__":
    main()
