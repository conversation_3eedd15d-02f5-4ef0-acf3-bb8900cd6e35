#!/usr/bin/env python3
"""
高精度点云精配准程序
使用ICP算法进行精配准，基于粗配准矩阵进行初始化
单位统一为毫米(mm)
"""

import numpy as np
import open3d as o3d
from scipy.spatial.distance import cdist
from scipy.optimize import minimize
import time
import os

class HighPrecisionRegistration:
    def __init__(self, voxel_size=0.1, max_iterations=1000, tolerance=1e-8):
        """
        初始化高精度配准器
        
        Args:
            voxel_size: 体素下采样大小 (mm)
            max_iterations: 最大迭代次数
            tolerance: 收敛容差
        """
        self.voxel_size = voxel_size
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading point cloud: {filepath}")
        
        # 读取xyz文件
        data = np.loadtxt(filepath)
        
        # 过滤掉全零点
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        
        print(f"Loaded {len(points)} valid points")
        
        # 创建Open3D点云对象
        pcd = o3d.geometry.PointCloud()
        pcd.points = o3d.utility.Vector3dVector(points)
        
        return pcd
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        print(f"Loading transformation matrix: {filepath}")
        matrix = np.loadtxt(filepath)
        print(f"Transformation matrix:\n{matrix}")
        return matrix
    
    def preprocess_point_cloud(self, pcd, voxel_size=None):
        """预处理点云：下采样、估计法向量"""
        if voxel_size is None:
            voxel_size = self.voxel_size
            
        print(f"Preprocessing point cloud with voxel size: {voxel_size}mm")
        
        # 下采样
        pcd_down = pcd.voxel_down_sample(voxel_size)
        print(f"Downsampled to {len(pcd_down.points)} points")
        
        # 估计法向量
        pcd_down.estimate_normals(
            search_param=o3d.geometry.KDTreeSearchParamHybrid(
                radius=voxel_size * 2, max_nn=30))
        
        return pcd_down
    
    def apply_transformation(self, pcd, transformation_matrix):
        """应用变换矩阵到点云"""
        pcd_transformed = pcd.transform(transformation_matrix)
        return pcd_transformed
    
    def compute_registration_error(self, source, target, transformation):
        """计算配准误差"""
        source_temp = source.transform(transformation)
        distances = np.asarray(source_temp.compute_point_cloud_distance(target))
        return np.mean(distances), np.std(distances)
    
    def icp_registration(self, source, target, initial_transformation, 
                        use_point_to_plane=True):
        """
        执行ICP配准
        
        Args:
            source: 源点云
            target: 目标点云
            initial_transformation: 初始变换矩阵
            use_point_to_plane: 是否使用点到平面ICP
        """
        print("Starting ICP registration...")
        
        # 设置ICP参数
        threshold = self.voxel_size * 2  # 对应距离阈值
        
        if use_point_to_plane:
            print("Using Point-to-Plane ICP")
            reg_p2p = o3d.pipelines.registration.registration_icp(
                source, target, threshold, initial_transformation,
                o3d.pipelines.registration.TransformationEstimationPointToPlane(),
                o3d.pipelines.registration.ICPConvergenceCriteria(
                    max_iteration=self.max_iterations,
                    relative_fitness=self.tolerance,
                    relative_rmse=self.tolerance
                )
            )
        else:
            print("Using Point-to-Point ICP")
            reg_p2p = o3d.pipelines.registration.registration_icp(
                source, target, threshold, initial_transformation,
                o3d.pipelines.registration.TransformationEstimationPointToPoint(),
                o3d.pipelines.registration.ICPConvergenceCriteria(
                    max_iteration=self.max_iterations,
                    relative_fitness=self.tolerance,
                    relative_rmse=self.tolerance
                )
            )
        
        return reg_p2p
    
    def multi_scale_icp(self, source, target, initial_transformation):
        """多尺度ICP配准"""
        print("Starting multi-scale ICP registration...")
        
        # 多个尺度的体素大小
        voxel_sizes = [self.voxel_size * 4, self.voxel_size * 2, self.voxel_size]
        current_transformation = initial_transformation.copy()
        
        for i, voxel_size in enumerate(voxel_sizes):
            print(f"\nScale {i+1}/{len(voxel_sizes)}: voxel_size = {voxel_size}mm")
            
            # 预处理点云
            source_down = self.preprocess_point_cloud(source, voxel_size)
            target_down = self.preprocess_point_cloud(target, voxel_size)
            
            # ICP配准
            reg_result = self.icp_registration(
                source_down, target_down, current_transformation)
            
            current_transformation = reg_result.transformation
            
            # 计算配准误差
            mean_error, std_error = self.compute_registration_error(
                source_down, target_down, current_transformation)
            
            print(f"Scale {i+1} results:")
            print(f"  Fitness: {reg_result.fitness:.6f}")
            print(f"  RMSE: {reg_result.inlier_rmse:.6f}mm")
            print(f"  Mean error: {mean_error:.6f}mm")
            print(f"  Std error: {std_error:.6f}mm")
        
        return current_transformation, reg_result
    
    def save_transformation_matrix(self, matrix, filepath):
        """保存变换矩阵"""
        print(f"Saving transformation matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.12f')
        print("Matrix saved successfully")

def main():
    # 初始化配准器
    registrator = HighPrecisionRegistration(
        voxel_size=0.05,  # 0.05mm体素大小，超高精度
        max_iterations=2000,
        tolerance=1e-10
    )
    
    print("=== 高精度点云精配准程序 ===")
    print("单位: 毫米(mm)")
    print()
    
    # 加载点云数据
    red_pcd = registrator.load_point_cloud("待配平面/red.xyz")
    blue_pcd = registrator.load_point_cloud("待配平面/blue.xyz")
    green_pcd = registrator.load_point_cloud("待配平面/green.xyz")
    
    # 加载粗配准矩阵
    b2r_coarse = registrator.load_transformation_matrix("b2r.txt")
    g2r_coarse = registrator.load_transformation_matrix("g2r.txt")
    
    print("\n=== Blue to Red 精配准 ===")
    start_time = time.time()
    
    # Blue到Red的精配准
    b2r_fine_matrix, b2r_result = registrator.multi_scale_icp(
        blue_pcd, red_pcd, b2r_coarse)
    
    b2r_time = time.time() - start_time
    print(f"Blue to Red 配准完成，耗时: {b2r_time:.2f}秒")
    
    print("\n=== Green to Red 精配准 ===")
    start_time = time.time()
    
    # Green到Red的精配准
    g2r_fine_matrix, g2r_result = registrator.multi_scale_icp(
        green_pcd, red_pcd, g2r_coarse)
    
    g2r_time = time.time() - start_time
    print(f"Green to Red 配准完成，耗时: {g2r_time:.2f}秒")
    
    # 保存精配准结果
    registrator.save_transformation_matrix(b2r_fine_matrix, "b2r_fine.txt")
    registrator.save_transformation_matrix(g2r_fine_matrix, "g2r_fine.txt")
    
    # 输出最终结果
    print("\n=== 精配准结果总结 ===")
    print(f"Blue to Red 精配准矩阵:")
    print(b2r_fine_matrix)
    print(f"最终RMSE: {b2r_result.inlier_rmse:.6f}mm")
    print(f"适应度: {b2r_result.fitness:.6f}")
    
    print(f"\nGreen to Red 精配准矩阵:")
    print(g2r_fine_matrix)
    print(f"最终RMSE: {g2r_result.inlier_rmse:.6f}mm")
    print(f"适应度: {g2r_result.fitness:.6f}")
    
    print("\n精配准矩阵已保存到:")
    print("- b2r_fine.txt (Blue to Red)")
    print("- g2r_fine.txt (Green to Red)")

if __name__ == "__main__":
    main()
