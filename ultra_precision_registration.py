#!/usr/bin/env python3
"""
超高精度点云精配准程序 - 目标精度: 0.1mm
使用多重优化策略实现超高精度配准
"""

import numpy as np
from scipy.spatial.distance import cdist
from scipy.spatial import cKDTree
from scipy.optimize import minimize
import time

class UltraPrecisionICP:
    def __init__(self, target_precision=0.1, max_iterations=2000, tolerance=1e-12):
        """
        超高精度ICP配准器
        
        Args:
            target_precision: 目标精度 (mm)
            max_iterations: 最大迭代次数
            tolerance: 收敛容差
        """
        self.target_precision = target_precision
        self.max_iterations = max_iterations
        self.tolerance = tolerance
        
    def load_point_cloud(self, filepath):
        """加载点云数据"""
        print(f"Loading point cloud: {filepath}")
        data = np.loadtxt(filepath)
        non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
        points = data[non_zero_mask, :3]
        print(f"Loaded {len(points)} valid points")
        return points
    
    def load_transformation_matrix(self, filepath):
        """加载变换矩阵"""
        print(f"Loading transformation matrix: {filepath}")
        matrix = np.loadtxt(filepath)
        return matrix
    
    def adaptive_downsample(self, points, target_density=100000):
        """自适应下采样 - 保持高密度采样"""
        if len(points) <= target_density:
            return points
        
        # 使用均匀网格采样保持空间分布
        min_coords = np.min(points, axis=0)
        max_coords = np.max(points, axis=0)
        
        # 计算网格大小
        volume = np.prod(max_coords - min_coords)
        grid_size = (volume / target_density) ** (1/3)
        
        # 网格索引
        grid_indices = np.floor((points - min_coords) / grid_size).astype(int)
        
        # 每个网格保留一个代表点（选择最接近网格中心的点）
        unique_grids = {}
        for i, grid_idx in enumerate(grid_indices):
            key = tuple(grid_idx)
            if key not in unique_grids:
                unique_grids[key] = []
            unique_grids[key].append(i)
        
        # 选择每个网格的代表点
        selected_indices = []
        for grid_points in unique_grids.values():
            if len(grid_points) == 1:
                selected_indices.append(grid_points[0])
            else:
                # 选择最接近网格中心的点
                grid_center = np.mean(points[grid_points], axis=0)
                distances = np.linalg.norm(points[grid_points] - grid_center, axis=1)
                best_idx = grid_points[np.argmin(distances)]
                selected_indices.append(best_idx)
        
        downsampled = points[selected_indices]
        print(f"Adaptive downsampled from {len(points)} to {len(downsampled)} points")
        return downsampled
    
    def apply_transformation(self, points, transformation_matrix):
        """应用变换矩阵"""
        ones = np.ones((points.shape[0], 1))
        homogeneous_points = np.hstack([points, ones])
        transformed_homogeneous = homogeneous_points @ transformation_matrix.T
        return transformed_homogeneous[:, :3]
    
    def robust_nearest_neighbors(self, source_points, target_points, max_distance=None):
        """鲁棒最近邻搜索"""
        tree = cKDTree(target_points)
        distances, indices = tree.query(source_points)

        if max_distance is not None:
            valid_mask = distances < max_distance
            return distances, indices, valid_mask

        return distances, indices, np.ones(len(distances), dtype=bool)
    
    def weighted_transformation_estimation(self, source_points, target_points, weights=None):
        """加权变换估计"""
        if weights is None:
            weights = np.ones(len(source_points))
        
        # 加权质心
        total_weight = np.sum(weights)
        source_centroid = np.sum(source_points * weights[:, np.newaxis], axis=0) / total_weight
        target_centroid = np.sum(target_points * weights[:, np.newaxis], axis=0) / total_weight
        
        # 去中心化
        source_centered = source_points - source_centroid
        target_centered = target_points - target_centroid
        
        # 加权协方差矩阵
        H = (source_centered * weights[:, np.newaxis]).T @ target_centered
        
        # SVD分解
        U, S, Vt = np.linalg.svd(H)
        R = Vt.T @ U.T
        
        # 确保旋转矩阵的行列式为正
        if np.linalg.det(R) < 0:
            Vt[-1, :] *= -1
            R = Vt.T @ U.T
        
        # 计算平移
        t = target_centroid - R @ source_centroid
        
        # 构建变换矩阵
        transformation = np.eye(4)
        transformation[:3, :3] = R
        transformation[:3, 3] = t
        
        return transformation
    
    def multi_scale_ultra_precision_icp(self, source_points, target_points, initial_transformation):
        """多尺度超高精度ICP"""
        print("Starting ultra-precision multi-scale ICP...")
        
        # 多个精度级别的配准
        precision_levels = [
            {'density': 50000, 'max_dist': 2.0, 'iterations': 200},   # 粗精度
            {'density': 100000, 'max_dist': 1.0, 'iterations': 300},  # 中精度
            {'density': 200000, 'max_dist': 0.5, 'iterations': 500},  # 高精度
            {'density': 300000, 'max_dist': 0.2, 'iterations': 800},  # 超高精度
        ]
        
        current_transformation = initial_transformation.copy()
        
        for level, params in enumerate(precision_levels):
            print(f"\nPrecision Level {level+1}/{len(precision_levels)}")
            print(f"Target density: {params['density']}, Max distance: {params['max_dist']}mm")
            
            # 自适应采样
            source_sample = self.adaptive_downsample(source_points, params['density'])
            target_sample = self.adaptive_downsample(target_points, params['density'])
            
            # 执行该级别的ICP
            level_transformation, final_error = self.precision_icp_iteration(
                source_sample, target_sample, current_transformation,
                max_distance=params['max_dist'],
                max_iterations=params['iterations']
            )
            
            current_transformation = level_transformation
            print(f"Level {level+1} final error: {final_error:.6f}mm")
            
            # 如果达到目标精度，提前退出
            if final_error < self.target_precision:
                print(f"Target precision {self.target_precision}mm achieved!")
                break
        
        return current_transformation, final_error
    
    def precision_icp_iteration(self, source_points, target_points, initial_transformation,
                               max_distance=0.5, max_iterations=500):
        """精密ICP迭代"""
        current_transformation = initial_transformation.copy()
        prev_error = float('inf')
        current_error = float('inf')

        for iteration in range(max_iterations):
            # 应用当前变换
            transformed_source = self.apply_transformation(source_points, current_transformation)

            # 鲁棒最近邻搜索
            distances, indices, valid_mask = self.robust_nearest_neighbors(
                transformed_source, target_points, max_distance)

            if np.sum(valid_mask) < 50:  # 降低最小对应点要求
                print(f"Insufficient correspondences at iteration {iteration}: {np.sum(valid_mask)} points")
                # 如果对应点太少，增大搜索距离
                max_distance *= 1.5
                distances, indices, valid_mask = self.robust_nearest_neighbors(
                    transformed_source, target_points, max_distance)
                if np.sum(valid_mask) < 20:
                    print(f"Still insufficient correspondences, stopping iteration")
                    break

            # 获取有效对应点
            valid_source = transformed_source[valid_mask]
            valid_target = target_points[indices[valid_mask]]
            valid_distances = distances[valid_mask]

            # 计算权重（距离越小权重越大）
            weights = np.exp(-valid_distances / (max_distance / 3))

            # 加权变换估计
            delta_transformation = self.weighted_transformation_estimation(
                valid_source, valid_target, weights)

            # 更新变换
            current_transformation = delta_transformation @ current_transformation

            # 计算误差
            current_error = np.mean(valid_distances)
            error_change = abs(prev_error - current_error)

            # 检查收敛
            if error_change < self.tolerance:
                print(f"Converged at iteration {iteration}, error: {current_error:.6f}mm")
                break

            prev_error = current_error

            if iteration % 100 == 0:
                print(f"Iteration {iteration}: error = {current_error:.6f}mm, valid points = {np.sum(valid_mask)}")

        return current_transformation, current_error
    
    def final_refinement(self, source_points, target_points, transformation):
        """最终精化 - 使用全点云进行微调"""
        print("Final ultra-precision refinement...")
        
        # 使用更大的采样进行最终精化
        max_points = 500000
        if len(source_points) > max_points:
            source_refined = self.adaptive_downsample(source_points, max_points)
        else:
            source_refined = source_points
            
        if len(target_points) > max_points:
            target_refined = self.adaptive_downsample(target_points, max_points)
        else:
            target_refined = target_points
        
        # 执行最终精化
        final_transformation, final_error = self.precision_icp_iteration(
            source_refined, target_refined, transformation,
            max_distance=0.1, max_iterations=200
        )
        
        return final_transformation, final_error
    
    def save_transformation_matrix(self, matrix, filepath):
        """保存变换矩阵（高精度格式）"""
        print(f"Saving ultra-precision transformation matrix to: {filepath}")
        np.savetxt(filepath, matrix, fmt='%.15f')
        print("Matrix saved successfully")

def main():
    # 设置随机种子
    np.random.seed(42)
    
    # 初始化超高精度配准器
    ultra_icp = UltraPrecisionICP(
        target_precision=0.1,  # 目标精度0.1mm
        max_iterations=2000,
        tolerance=1e-12
    )
    
    print("=== 超高精度点云精配准程序 ===")
    print("目标精度: 0.1mm")
    print("单位: 毫米(mm)")
    print()
    
    # 加载数据
    red_points = ultra_icp.load_point_cloud("待配平面/red.xyz")
    blue_points = ultra_icp.load_point_cloud("待配平面/blue.xyz")
    green_points = ultra_icp.load_point_cloud("待配平面/green.xyz")
    
    b2r_coarse = ultra_icp.load_transformation_matrix("b2r.txt")
    g2r_coarse = ultra_icp.load_transformation_matrix("g2r.txt")
    
    print("\n=== Blue to Red 超高精度配准 ===")
    start_time = time.time()
    
    # 多尺度超高精度配准
    b2r_ultra_matrix, b2r_error = ultra_icp.multi_scale_ultra_precision_icp(
        blue_points, red_points, b2r_coarse)
    
    # 最终精化
    b2r_final_matrix, b2r_final_error = ultra_icp.final_refinement(
        blue_points, red_points, b2r_ultra_matrix)
    
    b2r_time = time.time() - start_time
    print(f"Blue to Red 超高精度配准完成，耗时: {b2r_time:.2f}秒")
    
    print("\n=== Green to Red 超高精度配准 ===")
    start_time = time.time()
    
    # 多尺度超高精度配准
    g2r_ultra_matrix, g2r_error = ultra_icp.multi_scale_ultra_precision_icp(
        green_points, red_points, g2r_coarse)
    
    # 最终精化
    g2r_final_matrix, g2r_final_error = ultra_icp.final_refinement(
        green_points, red_points, g2r_ultra_matrix)
    
    g2r_time = time.time() - start_time
    print(f"Green to Red 超高精度配准完成，耗时: {g2r_time:.2f}秒")
    
    # 保存超高精度结果
    ultra_icp.save_transformation_matrix(b2r_final_matrix, "b2r_ultra_precision.txt")
    ultra_icp.save_transformation_matrix(g2r_final_matrix, "g2r_ultra_precision.txt")
    
    # 输出结果
    print("\n=== 超高精度配准结果 ===")
    print(f"Blue to Red:")
    print(f"  最终误差: {b2r_final_error:.6f}mm")
    print(f"  目标达成: {'✅' if b2r_final_error <= 0.1 else '❌'}")
    print(b2r_final_matrix)
    
    print(f"\nGreen to Red:")
    print(f"  最终误差: {g2r_final_error:.6f}mm")
    print(f"  目标达成: {'✅' if g2r_final_error <= 0.1 else '❌'}")
    print(g2r_final_matrix)
    
    print("\n超高精度配准矩阵已保存:")
    print("- b2r_ultra_precision.txt")
    print("- g2r_ultra_precision.txt")

if __name__ == "__main__":
    main()
