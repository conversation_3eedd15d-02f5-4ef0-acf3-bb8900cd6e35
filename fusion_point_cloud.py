#!/usr/bin/env python3
"""
使用超高精度矩阵融合三个点云数据
将blue和green点云转换到red坐标系下，然后完全融合
"""

import numpy as np
import time

def load_point_cloud(filepath):
    """加载点云数据"""
    print(f"Loading point cloud: {filepath}")
    data = np.loadtxt(filepath)
    
    # 过滤掉全零点
    non_zero_mask = ~np.all(data[:, :3] == 0, axis=1)
    points = data[non_zero_mask, :3]
    
    print(f"Loaded {len(points)} valid points")
    return points

def load_transformation_matrix(filepath):
    """加载变换矩阵"""
    print(f"Loading transformation matrix: {filepath}")
    matrix = np.loadtxt(filepath)
    return matrix

def apply_transformation(points, transformation_matrix):
    """应用变换矩阵到点云"""
    print(f"Applying transformation to {len(points)} points...")
    
    # 转换为齐次坐标
    ones = np.ones((points.shape[0], 1))
    homogeneous_points = np.hstack([points, ones])
    
    # 应用变换矩阵
    transformed_homogeneous = homogeneous_points @ transformation_matrix.T
    
    # 转换回3D坐标
    transformed_points = transformed_homogeneous[:, :3]
    
    print(f"Transformation completed")
    return transformed_points

def compute_point_cloud_stats(points, name):
    """计算点云统计信息"""
    print(f"\n{name} 点云统计信息:")
    print(f"  点数量: {len(points):,}")
    print(f"  X范围: [{np.min(points[:, 0]):.3f}, {np.max(points[:, 0]):.3f}] mm")
    print(f"  Y范围: [{np.min(points[:, 1]):.3f}, {np.max(points[:, 1]):.3f}] mm")
    print(f"  Z范围: [{np.min(points[:, 2]):.3f}, {np.max(points[:, 2]):.3f}] mm")
    print(f"  质心: ({np.mean(points[:, 0]):.3f}, {np.mean(points[:, 1]):.3f}, {np.mean(points[:, 2]):.3f}) mm")

def save_point_cloud(points, filepath, add_color=False, color=None):
    """保存点云数据"""
    print(f"Saving point cloud to: {filepath}")
    
    if add_color and color is not None:
        # 添加颜色信息 (RGB)
        colors = np.full((len(points), 3), color, dtype=np.uint8)
        data_with_color = np.hstack([points, colors])
        np.savetxt(filepath, data_with_color, fmt='%.6f %.6f %.6f %d %d %d', 
                   header='X Y Z R G B', comments='')
    else:
        # 只保存XYZ坐标
        np.savetxt(filepath, points, fmt='%.6f')
    
    print(f"Saved {len(points)} points")

def fuse_point_clouds(red_points, blue_transformed, green_transformed):
    """融合三个点云"""
    print("\n=== 开始点云融合 ===")
    
    # 直接拼接所有点云
    fused_points = np.vstack([red_points, blue_transformed, green_transformed])
    
    print(f"融合完成:")
    print(f"  Red点云: {len(red_points):,} 点")
    print(f"  Blue点云(转换后): {len(blue_transformed):,} 点")
    print(f"  Green点云(转换后): {len(green_transformed):,} 点")
    print(f"  融合后总点数: {len(fused_points):,} 点")
    
    return fused_points

def create_colored_fusion(red_points, blue_transformed, green_transformed):
    """创建带颜色标识的融合点云"""
    print("\n=== 创建带颜色标识的融合点云 ===")
    
    # 为每个点云分配颜色
    red_color = [255, 0, 0]      # 红色
    blue_color = [0, 0, 255]     # 蓝色
    green_color = [0, 255, 0]    # 绿色
    
    # 创建颜色数组
    red_colors = np.full((len(red_points), 3), red_color, dtype=np.uint8)
    blue_colors = np.full((len(blue_transformed), 3), blue_color, dtype=np.uint8)
    green_colors = np.full((len(green_transformed), 3), green_color, dtype=np.uint8)
    
    # 拼接点云和颜色
    red_with_color = np.hstack([red_points, red_colors])
    blue_with_color = np.hstack([blue_transformed, blue_colors])
    green_with_color = np.hstack([green_transformed, green_colors])
    
    # 融合所有数据
    fused_colored = np.vstack([red_with_color, blue_with_color, green_with_color])
    
    print(f"带颜色融合点云创建完成: {len(fused_colored):,} 点")
    return fused_colored

def verify_transformation_quality(original_points, transformed_points, matrix, name):
    """验证变换质量"""
    print(f"\n=== {name} 变换质量验证 ===")
    
    # 计算变换前后的质心变化
    original_centroid = np.mean(original_points, axis=0)
    transformed_centroid = np.mean(transformed_points, axis=0)
    
    # 手动计算预期质心
    ones = np.ones((1, 1))
    homogeneous_centroid = np.hstack([original_centroid.reshape(1, -1), ones])
    expected_centroid = (homogeneous_centroid @ matrix.T)[:, :3].flatten()
    
    centroid_error = np.linalg.norm(transformed_centroid - expected_centroid)
    
    print(f"  原始质心: ({original_centroid[0]:.3f}, {original_centroid[1]:.3f}, {original_centroid[2]:.3f})")
    print(f"  变换后质心: ({transformed_centroid[0]:.3f}, {transformed_centroid[1]:.3f}, {transformed_centroid[2]:.3f})")
    print(f"  预期质心: ({expected_centroid[0]:.3f}, {expected_centroid[1]:.3f}, {expected_centroid[2]:.3f})")
    print(f"  质心误差: {centroid_error:.6f} mm")
    
    if centroid_error < 0.001:
        print(f"  ✅ 变换质量: 优秀")
    elif centroid_error < 0.01:
        print(f"  ✅ 变换质量: 良好")
    else:
        print(f"  ⚠️ 变换质量: 需要检查")

def main():
    print("=== 超高精度点云融合程序 ===")
    print("使用超高精度配准矩阵将三个点云融合到统一坐标系")
    print("单位: 毫米(mm)")
    print()
    
    start_time = time.time()
    
    # 1. 加载原始点云数据
    print("=== 步骤1: 加载原始点云数据 ===")
    red_points = load_point_cloud("待配平面/red.xyz")
    blue_points = load_point_cloud("待配平面/blue.xyz")
    green_points = load_point_cloud("待配平面/green.xyz")
    
    # 2. 加载超高精度变换矩阵
    print("\n=== 步骤2: 加载超高精度变换矩阵 ===")
    b2r_matrix = load_transformation_matrix("b2r_ultra_precision_final.txt")
    g2r_matrix = load_transformation_matrix("g2r_ultra_precision_final.txt")
    
    print("\nBlue到Red超高精度变换矩阵:")
    print(b2r_matrix)
    print("\nGreen到Red超高精度变换矩阵:")
    print(g2r_matrix)
    
    # 3. 计算原始点云统计信息
    print("\n=== 步骤3: 原始点云统计信息 ===")
    compute_point_cloud_stats(red_points, "Red")
    compute_point_cloud_stats(blue_points, "Blue")
    compute_point_cloud_stats(green_points, "Green")
    
    # 4. 应用超高精度变换
    print("\n=== 步骤4: 应用超高精度变换 ===")
    blue_transformed = apply_transformation(blue_points, b2r_matrix)
    green_transformed = apply_transformation(green_points, g2r_matrix)
    
    # 5. 验证变换质量
    verify_transformation_quality(blue_points, blue_transformed, b2r_matrix, "Blue到Red")
    verify_transformation_quality(green_points, green_transformed, g2r_matrix, "Green到Red")
    
    # 6. 计算变换后点云统计信息
    print("\n=== 步骤5: 变换后点云统计信息 ===")
    compute_point_cloud_stats(red_points, "Red (参考)")
    compute_point_cloud_stats(blue_transformed, "Blue (转换到Red坐标系)")
    compute_point_cloud_stats(green_transformed, "Green (转换到Red坐标系)")
    
    # 7. 融合点云
    print("\n=== 步骤6: 点云融合 ===")
    fused_points = fuse_point_clouds(red_points, blue_transformed, green_transformed)
    
    # 8. 计算融合后统计信息
    print("\n=== 步骤7: 融合后点云统计信息 ===")
    compute_point_cloud_stats(fused_points, "融合点云")
    
    # 9. 保存融合结果
    print("\n=== 步骤8: 保存融合结果 ===")
    
    # 保存完整融合点云
    save_point_cloud(fused_points, "fused_point_cloud.xyz")
    
    # 保存带颜色标识的融合点云
    fused_colored = create_colored_fusion(red_points, blue_transformed, green_transformed)
    
    # 保存带颜色的点云
    print("Saving colored fusion point cloud to: fused_point_cloud_colored.xyz")
    np.savetxt("fused_point_cloud_colored.xyz", fused_colored, 
               fmt='%.6f %.6f %.6f %d %d %d',
               header='X Y Z R G B', comments='')
    print(f"Saved {len(fused_colored)} points with color information")
    
    # 分别保存转换后的点云
    save_point_cloud(blue_transformed, "blue_transformed_to_red.xyz")
    save_point_cloud(green_transformed, "green_transformed_to_red.xyz")
    
    total_time = time.time() - start_time
    
    # 10. 输出最终总结
    print("\n" + "="*60)
    print("=== 超高精度点云融合完成 ===")
    print(f"✅ 处理时间: {total_time:.2f}秒")
    print(f"✅ 融合点云总数: {len(fused_points):,} 点")
    print(f"✅ 使用超高精度矩阵: 0.1mm级别精度")
    print()
    print("生成的文件:")
    print("- fused_point_cloud.xyz: 完整融合点云 (XYZ格式)")
    print("- fused_point_cloud_colored.xyz: 带颜色标识的融合点云 (XYZRGB格式)")
    print("- blue_transformed_to_red.xyz: Blue点云转换到Red坐标系")
    print("- green_transformed_to_red.xyz: Green点云转换到Red坐标系")
    print()
    print("颜色标识:")
    print("- 红色 (255,0,0): 原始Red点云")
    print("- 蓝色 (0,0,255): Blue点云转换到Red坐标系")
    print("- 绿色 (0,255,0): Green点云转换到Red坐标系")
    print()
    print("🎉 三个点云已成功融合到统一的Red坐标系下！")

if __name__ == "__main__":
    main()
